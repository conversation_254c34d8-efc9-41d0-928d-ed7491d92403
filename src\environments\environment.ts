    // This file can be replaced during build by using the `fileReplacements` array.
    // `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
    // The list of file replacements can be found in `angular.json`.

let api_url = "http://127.0.0.1:3210/api/";
export const environment = {
    APP_TITLE : 'Proceum | Become fluent in MEDICAL CONCEPTS',
    APP_DESC : 'With Accurate, UpToDate world class Medical Animations and Illustrations',
    APP_IMAGE: 'https://proceum.com/assets/images/Proceum_thumbnail.jpg',
    production: false,
    file_upload_size: 512000,//in kb
    page_size: 10,
    page_size_options: [10, 20, 50, 100],    
    editor_config: { 
    toolbar_Full :
    [
        ['Source','-','ExportPdf','NewPage','Preview','-','Templates'],
        ['Cut','Copy','Paste','PasteText','PasteFromWord','-','<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
        ['Undo','Redo','-','Find','Replace','-','SelectAll','RemoveFormat'],
        //['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField'],
        ['Bold','Italic','Underline','Strike','-','Subscript','Superscript'],
        ['Outdent','Indent','Blockquote'],
        ['JustifyLeft','JustifyCenter','JustifyRight','JustifyBlock'],
        ['Link','Unlink','Anchor'],
        ['Image','Flash','Table','HorizontalRule','Smiley','SpecialChar','PageBreak'],
        ['Styles','Format','Font','FontSize'],
        ['TextColor','BGColor'],
        ['Maximize', 'ShowBlocks','-'] 
    ],
    allowedContent : true,
    extraAllowedContent: "h3{clear};h2{line-height};h2 h3{margin-left,margin-top};mathElements.join( ' ' ) + '(*)[*]{*};img[data-mathml,data-custom-editor,role](Wirisformula)'",
    extraPlugins: 'format,font,colorbutton,justify,uploadimage,slideshow,ckeditor_wiris,abbr,liststyle',
    uploadUrl: api_url+'upload-files',
    // Configure your file manager integration. This example uses CKFinder 3 for PHP.
    filebrowserImageBrowseUrl: '/assets/ckeditor/plugins/ckfinder/samples/full-page-open.html?command=GetFiles&lang=en&type=Images&currentFolder=/images/content_images/',
    filebrowserImageUploadUrl: api_url+'upload-files',
    height: 360,
    //removeDialogTabs: 'image:advanced;link:advanced',
    removeButtons: 'Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Save,CopyFormatting,RemoveFormat,NewPage,Print,SetLanguage,ShowBlocks,AboutCKEditor4'
    },
    lang: "https://s3.ap-south-1.amazonaws.com/assets.proceum.com/lang_flags/4x3/",
    angularEditorConfig: {
    editable: true,
    spellcheck: true,
    toolbarHiddenButtons: [['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull', 'fontName', 'customClasses', 'insertImage', 'insertVideo', 'insertHorizontalRule']]
    },
    liteEditorConfig: {
    toolbar :
    [
        ['Source','-','Save','NewPage','Preview','-','Templates'],
        ['Cut','Copy','Paste','PasteText','PasteFromWord','-','Print', 'SpellChecker', 'Scayt'],
        ['Undo','Redo','-','Find','Replace','-','SelectAll','RemoveFormat'],
        //['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField'],
        ['Bold','Italic','Underline','Strike','-','Subscript','Superscript'],
        ['NumberedList','BulletedList','-','Outdent','Indent','Blockquote'],
        ['JustifyLeft','JustifyCenter','JustifyRight','JustifyBlock'],
        ['Link','Unlink','Anchor'],
        ['Image','Flash','Table','HorizontalRule','Smiley','SpecialChar','PageBreak'],
        ['Styles','Format','Font','FontSize'],
        ['TextColor','BGColor'],
        ['Maximize', 'ShowBlocks','-']
    ],
    allowedContent : true,
    extraAllowedContent: "h3{clear};h2{line-height};h2 h3{margin-left,margin-top};mathElements.join( ' ' ) + '(*)[*]{*};img[data-mathml,data-custom-editor,role](Wirisformula)'",
    extraPlugins: 'format,font,colorbutton,justify,uploadimage,ckeditor_wiris',
    //uploadUrl: api_url+'upload-files',
    filebrowserBrowseUrl:'',
    // Configure your file manager integration. This example uses CKFinder 3 for PHP.
    //filebrowserImageBrowseUrl: '/assets/ckeditor/plugins/ckfinder/samples/full-page-open.html?command=GetFiles&lang=en&type=Images&currentFolder=/images/content_images/',
    filebrowserImageUploadUrl: api_url+'upload-files',
    height: 360,
    //removeDialogTabs: 'link:advanced',
    removeButtons: 'Cut,Copy,Paste,PasteText,PasteFromWord,Print,SpellChecker,Styles,Scayt,SelectAll,Templates,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Save,CopyFormatting,RemoveFormat,NewPage,Print,SetLanguage,ShowBlocks,AboutCKEditor4,Iframe,ExportPdf,Language,CreateDiv'
    },
    video_types: [{ name: "kPoint", value: 'KPOINT',img:'../../../assets/images/kpoint_k.png' }, { name: "YouTube", value: 'YOUTUBE',img:'../../../assets/images/youtube.png' }, { name: "VdoCipher", value: "VDO_CIPHER",img:'../../../assets/images/video-cipher.png'}, { name: "AppSquadz", value: "APP_SQUADZ",img:'../../../assets/images/app-squadz.png'}],
    ORGANIZATION_TYPES: [
    { value: '1', viewValue: 'University' },
    { value: '2', viewValue: 'College' },
    { value: '3', viewValue: 'Institute' }
    ],
    DISCOUNT_TYPES: [
    { value: 1, viewValue: 'Fixed Amount' },
    { value: 2, viewValue: 'Percentage' }
    ],

    PROCEUM_ADMIN_SPECIFIC_ROLES: {
        SUPER_ADMIN: 1,
        ADMIN: 16, //This role comes under SUPER_ADMIN
    },

    PARTNER_ADMIN_SPECIFIC_ROLES: {
        UNIVERSITY_ADMIN: 8,
        COLLEGE_ADMIN: 9,
        INSTITUTE_ADMIN: 10,
        UNIVERSITY_COLLEGE_ADMIN: 14,
    },

    ALL_ADMIN_SPECIFIC_ROLES: {
        SUPER_ADMIN: 1,
        ADMIN: 16,
        UNIVERSITY_ADMIN: 8,
        COLLEGE_ADMIN: 9,
        INSTITUTE_ADMIN: 10,
        UNIVERSITY_COLLEGE_ADMIN: 14,
    },

    ALL_ROLES: {
        SUPER_ADMIN: 1,
        STUDENT: 2,
        CONTENT_EDITOR: 3,
        REVIEWER_1: 4,
        REVIEWER_2: 5,
        REVIEWER_3: 6,
        APPROVER: 7,
        UNIVERSITY_ADMIN: 8,
        COLLEGE_ADMIN: 9,
        INSTITUTE_ADMIN: 10,
        INDIVIDUAL: 11,
        TEACHER: 12,
        FINANCE_USER: 13,
        UNIVERSITY_COLLEGE_ADMIN: 14,
        ASSOCIATE: 15,
        ADMIN : 16,
    },
    ACCESS_MATRIX_ROLES: {
        SUPER_ADMIN: 1,
        CONTENT_EDITOR: 3,
        REVIEWER_1: 4,
        REVIEWER_2: 5,
        REVIEWER_3: 6,
        APPROVER: 7,
        UNIVERSITY_ADMIN: 8,
        COLLEGE_ADMIN: 9,
        INSTITUTE_ADMIN: 10,
        TEACHER: 12,
        UNIVERSITY_COLLEGE_ADMIN: 14,
        ASSOCIATE: 15,
        ADMIN : 16,
    },
    MAX_LENGTH_1000 : 1000,
    CONTENT_USER_ROLES:[3,4,5,6,7,13],
    DISABLED_USER_ROLES_FOR_PROCEUM: [2,12],
    DISABLED_USER_ROLES_FOR_ORGANIZATION: [1, 3, 4, 5, 6, 7, 16],

    apiUrl: api_url,
    videocryptapiURL: "https://api.videocrypt.com/getVideoDetails",
    APP_BASE_URL: 'http://localhost:4200/',
    firebaseConfig: {
        apiKey: "AIzaSyA8ttfPXEWre9okCwP45AJ-mjyKdheGBZA",
        authDomain: "proceum-uat.firebaseapp.com",
        projectId: "proceum-uat",
        storageBucket: "proceum-uat.appspot.com",
        messagingSenderId: "413753553408",
        appId: "1:413753553408:web:ec2eb9fedbc16fb4dc9ee5",
        domain: "https://proceum.page.link"
    },

    /*
    * Below array used to check domian or subdomian from in app users or partners
    */
    INAPP_DOMAINS_ARRAY: ["localhost:4200","dev", "master", "uat"],
    PACKAGE_DEFAULT_IMG: '../../../assets/images/steth_new.jpg',

    /* Change based on dev or uat (hhtp or https) */
    SSL_ORIGIN: 'https',
    /* Change based on dev or uat or prod*/
    APP_SUB_DOMAIN: 'localhost:4200',
    APP_DOMAIN: 'localhost:4200',

    CURRENCY_CONVERTER_LINK: 'https://www.xe.com/currencyconverter/convert/?Amount=1&From=INR&To=USD',
    SYSTEM_MODE_TOKEN: 'sparshmaintenance',
    IP_INFO_TOKEN: 'a5444855afa7f6',

    DESIGNATION: {
        Dean: 1,
        Principal: 2,
        HoD: 3,
        Professor: 4,
        Associate_Professor: 5,
        Assistance_Professor: 6,
        Tutor: 7,
        Rector: 8,
        Vice_Rector: 9,
        Vice_Dean: 10,
        None: 11
    },
    HIGH_PROFILE_DESIGNATIONS: ['Rector', 'Vice Rector', 'Vice_Dean', 'Dean', 'HoD','Dean/Principal','Vice Dean/Principal'],
    HIGH_PROFILE_DESIGNATIONS_IDS:[1,3,8,9,10],    
    recaptcha: {
        siteKey: '6LfISo0iAAAAAJyVk3FeofI2HCRrn0fyaC9HHEnk',
    },
    version:'v1',
    MICROSCOPY_TYPE_ID :4,
    APP_SQUDZ_DOMAIN : "https://www.videocrypt.com/website/player_code?id=",
    VDO_CIPHER : "https://player.vdocipher.com/v2/?",
    VDO_CIPHER1 : "https://player1.vdocipher.com/v2/?",
    VDO_CIPHER_COUNTRY : ["KZ","IN"],
    PLAY_STORE : "https://play.google.com/store/search?q=proceum&c=apps",
    APP_STORE : "https://apps.apple.com/us/app/proceum/id6470613362",
    APP_VERSION : '09.0.0',
};

/*
* For easier debugging in development mode, you can import the following file
* to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
*
* This import should be commented out in production mode because it will have a negative impact
* on performance if an error is thrown.
*/
 // import 'zone.js/dist/zone-error';  // Included with Angular CLI.
