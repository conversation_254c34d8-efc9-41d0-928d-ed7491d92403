<div class="mn_dshbrd">

    <!-- Popup for select question for particular topic start here -->

    <div class="cust_mdl_par {{ modal_popup ? 'cust_mdl_opn' : '' }}">
        <div class="cust_mdl_chl">
          <div class="pg_ttl mpng_pp_hd">
            <strong>{{"admin.assessment.create.select_questions"|translate}} : </strong> {{ topic_text }}
            <span class="spacer"></span>
            <button mat-icon-button (click)="closeModal()">
              <mat-icon>close</mat-icon>
            </button>
          </div>
          <div class="tbbd_bdy">
            <div class="hgt_20"></div>
            
            <mat-tab-group (selectedTabChange)="questionTab($event)" animationDuration="0.15s" [(selectedIndex)]="question_tab">
              <mat-tab label="{{'admin.assessment.create.all_questions'|translate}}">
                <div class="dsh_brd_crds">
                  <mat-form-field appearance="outline" class="float_right">
                    <input [(ngModel)]="question_search_box" (keyup)="$event.keyCode == 13 ? doFilter() : ''" matInput placeholder="{{'search_text'|translate}}" autocomplete="off" />
                    <mat-icon matSuffix (click)="doFilter()">search</mat-icon>
                  </mat-form-field>
                </div>
    
                <div class="rsp_tbl popup_mid_div">
                  <table mat-table [dataSource]="dataSourceQuestions" class="whtspc_nml">
                    <ng-container matColumnDef="select">
                      <th mat-header-cell *matHeaderCellDef>{{"select_text"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <mat-checkbox [disabled]="source_ids.includes(element['pk_id']) ? false : isDisabled" [value]="element['pk_id']" [checked]="source_ids.includes(element['pk_id'])" (change)="selectQuestion($event, element['pk_id'])"></mat-checkbox>
                      </td>
                    </ng-container>
        
                    <ng-container matColumnDef="title">
                      <th mat-header-cell *matHeaderCellDef>{{"admin.assessment.create.questions"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <span *ngIf="highlighter" [innerHTML]="element.question_text | highlight:question_search_box"></span>
                        <span *ngIf="!highlighter" [innerHTML]="element.question_text"></span>
                      </td> 
                    </ng-container>

                    <ng-container matColumnDef="question_type">
                      <th mat-header-cell *matHeaderCellDef>{{"admin.assessment.create.questions_type"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <span [innerHTML]="element.question_type"></span>
                        <!-- <span *ngIf="element.q_source_value != null"><img [src]="bucket_url+element.q_source_value" /></span> -->
                      </td> 
                    </ng-container>
        
                    <tr mat-header-row *matHeaderRowDef="displayedQuestionsColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedQuestionsColumns"></tr>
                    <tr class="mat-row no_rcrds_fd" *matNoDataRow>
                      <td colspan="5" style="text-align: center; margin: 20px" class="mat-cell"
                        [attr.colspan]="displayedQuestionsColumns.length">
                        {{"no_records_found_text"|translate}}
                      </td>
                    </tr>
                  </table>
                </div>
                <mat-paginator #paginator [pageSizeOptions]="page_size_options" [length]="totalSize"
                  [pageSize]="pageSize" [showFirstLastButtons]="true" (page)="contentGetServerData($event)"></mat-paginator>
    
              </mat-tab>
              <mat-tab label="{{'admin.assessment.create.selected_questions'|translate}}">
                <div class="dsh_brd_crds">
                  <mat-form-field appearance="outline" class="float_right">
                    <input [(ngModel)]="question_search_box" (keyup)="$event.keyCode == 13 ? doFilter() : ''" matInput placeholder="{{'search_text'|translate}}" autocomplete="off" />
                    <mat-icon matSuffix (click)="doFilter()">search</mat-icon>
                  </mat-form-field>
                </div>
    
                <div class="rsp_tbl popup_mid_div">
                  <table mat-table [dataSource]="dataSourceQuestions" class="whtspc_nml">
                    <ng-container matColumnDef="select">
                      <th mat-header-cell *matHeaderCellDef>{{"select_text"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <mat-checkbox [value]="element['pk_id']" [checked]="source_ids.includes(element['pk_id'])" (change)="selectQuestion($event, element['pk_id'])"></mat-checkbox>
                      </td>
                    </ng-container>
        
                    <ng-container matColumnDef="title">
                      <th mat-header-cell *matHeaderCellDef>{{"admin.assessment.create.questions"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <span *ngIf="highlighter" [innerHTML]="element.question_text | highlight:question_search_box"></span>
                        <span *ngIf="!highlighter" [innerHTML]="element.question_text"></span>
                      </td> 
                    </ng-container>

                    <ng-container matColumnDef="question_type">
                      <th mat-header-cell *matHeaderCellDef>{{"admin.assessment.create.questions_type"|translate}}</th>
                      <td mat-cell *matCellDef="let element">
                        <span [innerHTML]="element.question_type"></span>
                      </td> 
                    </ng-container>
        
                    
                    <tr mat-header-row *matHeaderRowDef="displayedQuestionsColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedQuestionsColumns"></tr>
                    <tr class="mat-row no_rcrds_fd" *matNoDataRow>
                      <td colspan="5" style="text-align: center; margin: 20px" class="mat-cell"
                        [attr.colspan]="displayedQuestionsColumns.length">
                        {{"no_records_found_text"|translate}}
                      </td>
                    </tr>
                  </table>
                </div>
                <mat-paginator #paginator [pageSizeOptions]="page_size_options" [length]="totalSize"
                  [pageSize]="pageSize" [showFirstLastButtons]="true" (page)="contentGetServerData($event)"></mat-paginator>
    
              </mat-tab>
            </mat-tab-group>
            
            <hr />
            <div class="float_right">
              <button mat-raised-button (click)="closeModal()" class="mr_1 gry_btn">
                {{"close"|translate}}
              </button>
              <button mat-raised-button (click)="getSelectQuestions()" color="accent">
                {{"save_text"|translate}}
              </button>
            </div>
            <div class="hgt_20"></div>
          </div>
        </div>
    </div>
    <!-- Popup for select question for particular topic end here-->
    <div class="tb_brcrm">
        <div class="_brcrm_ac">
          <img src="../../../../assets/images/Assessments.svg" />
        </div>
        <div>
          <div class="_brcrm_aa">{{"admin.assessment.create.page_title"|translate}}</div>
          <div class="_brcrm_ab"><span routerLink="/">Home </span> <mat-icon>chevron_right</mat-icon> {{"admin.assessment.create.page_title"|translate}}</div>
        </div>
    </div>
    <!-- <div class="brd_crm">{{"proceum_text"|translate}} &raquo;
        <strong>{{"admin.assessment.create.page_title"|translate}} </strong>
    </div>
    <div class="d-flex rsp_fw">
        <div class="pg_ttl">{{"admin.assessment.create.page_title"|translate}}</div>
        <div class="spacer"></div>
    </div> -->
    <div class="brd_crm"></div>
    <div class="rqrd_txtinfo rqrd_txtinfo_bg"><mat-icon>report_problem</mat-icon>{{"admin.assessment.create.note"|translate}} </div>
    <form id="create_class_form" #create_class_form="ngForm" name="create_class_form"
        (ngSubmit)="create_class_form.valid && CreateAssessment()" novalidate>
        <div class="dsh_brd_crds">
            <div class="w_100p">
                <span>{{"admin.assessment.create.assessment_name"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline" class="w_100p">
                    <input matInput [(ngModel)]="assessment_name" #assessment_name_name="ngModel"
                        name="assessment_name_name" maxlength="50"
                        placeholder='{{"enter"|translate}} {{"admin.assessment.create.assessment_name"|translate}}' autocomplete="off"
                        required>
                </mat-form-field>
            </div>
            <div class="clear_fix"></div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.assessment_type"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="assessment_type" name="assessment_type_name" required>
                        <mat-option value="">{{"select_text"|translate}}
                            {{"admin.assessment.create.assessment_type"|translate}}</mat-option>
                        <mat-option *ngFor="let item of assessment_types" [value]="item['pk_id']">{{
                            item["assessment_type"] }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.timezone"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline">
                    <mat-select value="" [(ngModel)]="timezone" name="timezone_name" required (ngModelChange)="getDateTime($event);">
                        <mat-option value="">{{"select_text"|translate}}</mat-option>
                        <mat-option *ngFor="let item of timezone_list" [value]="item['timezone']">{{
                            item["name"] }} ({{item['timezone']}})</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.start_date"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline">
                    <input matInput [(ngModel)]="start_date" name="start_date_name" readonly [matDatepicker]="picker" [min]="minDate" required placeholder='{{"admin.assessment.create.start_date"|translate}}' autocomplete="off" (ngModelChange)="getStartTime();">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.start_time"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline" class="w_47p">
                    <!-- <input type="time" matInput [(ngModel)]="start_time" name="start_time_name" required> -->
                    <mat-select value="" [(ngModel)]="start_time" name="start_time_name" required >
                        <mat-option value="">{{"select_text"|translate}}</mat-option>
                        <mat-option *ngFor="let item of timeOptions" [value]="item['value']">{{
                            item["label"] }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.each_question_duration"|translate}} <span
                        class="required">*</span></span>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="question_duration" name="question_duration_name" required>
                         
                        <mat-option value="">{{"select_text"|translate}}</mat-option>
                        <mat-option *ngFor="let tl of timeList" value="{{tl['id']}}" >{{tl['value']}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.each_question_duration"|translate}} <span
                        class="required">*</span></span>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="question_duration" name="question_duration_name" required>
                         
                        <mat-option value="">{{"select_text"|translate}}</mat-option>
                        <mat-option *ngFor="let tl of timeList" value="{{tl['id']}}" >{{tl['value']}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>

            <div class="cm_fltr_sc mr_1" *ngIf="curriculum_list != undefined">
                <div class="clear_fix"></div>
                <div>{{"admin.assessment.create.qbank"|translate}} <span class="required">*</span></div>
                <mat-form-field appearance="outline">
                    <mat-select [(ngModel)]="curriculum_id" #curriculum_id_name="ngModel" ngModel
                        name="curriculum_id_name" placeholder='{{"admin.assessment.create.qbank"|translate}}'
                        (ngModelChange)="setCourseText($event);getLabels();" required>
                        <mat-option *ngFor="let value of curriculum_list" [value]="value['pk_id']"
                            [matTooltip]="value['curriculumn_name']">{{ value["curriculumn_name"] }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1" *ngFor="let value of curriculum_labels; let i = index">
                <span>{{ ucFirst(value["display_label"]) }}</span>
                <div class="clear_fix"></div>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'select_text'|translate}} {{ ucFirst(value['display_label']) }}" name="selected_level_name"
                        [(ngModel)]="selected_level[value['level_number']]"
                        (selectionChange)="getLevels(value['level_number'])">
                        <mat-option>
                            <ngx-mat-select-search
                                [noEntriesFoundLabel]="'No '+ ucFirst(value['display_label'])+' found'"
                                name="search_topic" ngModel
                                (ngModelChange)="searchLevelByName($event,value['level_number'])"
                                [placeholderLabel]="'Search '+ucFirst(value['display_label'])">
                                {{"select_text"|translate}}
                                {{ucFirst(value['display_label'])}}
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let level of level_options[value['level_number']]" [value]="level['pk_id']"
                            [matTooltip]="level['level_name']">{{level["level_name"] }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.questions_count"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline" class="w_100p sffx_tp_0">
                    <input type="number" [max]="questions_count" matInput placeholder="" name="questions_count_name" appNumbersonly
                        [(ngModel)]="questions_count" />
                    <div matSuffix>/{{total_count}}</div>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc">
                <button type="button" mat-raised-button color="accent" class="mr_1"
                    [disabled]="questions_count<=0 || total_count==0 || total_count < questions_count"
                    (click)="addQuestionCount()">
                    <!-- <mat-icon>add</mat-icon> --> {{"admin.assessment.create.add"|translate}} 
                </button>
            </div>
            <div class="clear_fix"></div>
            <div class="rsp_tbl">
                <table mat-table [dataSource]="dataSource">
                    <ng-container matColumnDef="Subject">
                        <th mat-header-cell *matHeaderCellDef> {{"s_no_text"|translate}} </th>
                        <td mat-cell *matCellDef="let element; let s_no=index"> {{s_no+1}} </td>
                        <td mat-footer-cell *matFooterCellDef> {{"admin.assessment.create.total"|translate}}  </td>
                    </ng-container>
                    <ng-container matColumnDef="Chapter">
                        <th mat-header-cell *matHeaderCellDef> {{"admin.assessment.create.qbank"|translate}} </th>
                        <td mat-cell *matCellDef="let element"> {{element.course_qbank_text}} </td>
                        <td mat-footer-cell *matFooterCellDef>  </td>
                    </ng-container>
                    <ng-container matColumnDef="Topic">
                        <th mat-header-cell *matHeaderCellDef> {{"admin.assessment.create.topic_path"|translate}} </th>
                        <td mat-cell *matCellDef="let element"> {{element.topic_text}} </td>
                        <td mat-footer-cell *matFooterCellDef> ({{"admin.assessment.create.question_count_error_msg"|translate}}) </td>
                    </ng-container>
                    <ng-container matColumnDef="Count">
                        <th mat-header-cell *matHeaderCellDef> {{"admin.assessment.create.count"|translate}} </th>
                        <td mat-cell *matCellDef="let element"> {{element.questions_count}} </td>
                        <td mat-footer-cell *matFooterCellDef > 
                            <mat-form-field appearance="none">
                                <input matInput placeholder='{{"admin.assessment.create.total"|translate}}' [(ngModel)]="question_total" name="question_total_name"
                                    autocomplete="off" readonly required>
                            </mat-form-field>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="Questions">
                        <th mat-header-cell *matHeaderCellDef> {{"admin.assessment.create.select_questions"|translate}} </th>
                        <td mat-cell *matCellDef="let element">
                            <button type="button" (click)="getQuetions(element)"
                                mat-raised-button color="accent">{{"admin.assessment.create.select_questions"|translate}}</button>
                        </td>
                        <td mat-footer-cell *matFooterCellDef> </td>
                    </ng-container>
                    <ng-container matColumnDef="Action">
                        <th mat-header-cell *matHeaderCellDef> {{"admin.assessment.create.actions"|translate}} </th>
                        <td mat-cell *matCellDef="let element; let topic_index=index">
                            <span (click)="removeTopic(topic_index,element.topic)">
                                <mat-icon color="warn" matTooltip="Delete">remove_circle_outline</mat-icon>
                            </span>
                        </td>
                        <td mat-footer-cell *matFooterCellDef>  </td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                    <tr class="mat-row no_rcrds_fd" *matNoDataRow>
                        <td class="mat-cell" colspan="6">{{"no_records_found_text"|translate}}</td>
                    </tr>
                    <tr mat-footer-row *matFooterRowDef="displayedColumns; sticky: true"></tr>
                </table>
            </div>
            <hr class="hgt_10">
            <div class="cm_fltr_sc mr_1" *ngIf="is_university">
                <span>{{"admin.assessment.create.organization_type"|translate}} <span class="required">*</span></span>
                <mat-form-field appearance="outline" class="w_100p">
                    <mat-select placeholder='{{"admin.assessment.create.select_organization_type"|translate}}'
                        name="organization_edit" #organization_edit="ngModel" [(ngModel)]="organization_type_id"
                        (ngModelChange)="onOrganizationTypeChange()">
                        <mat-option *ngFor="let item of organization_types" [value]="item.value">{{ item.viewValue }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1" *ngIf="is_university">
                <span>{{ organization_type_name ? organization_type_name : 'Organization List' }} </span>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'select_text'|translate}} {{ organization_type_name }}" name="organization_list_edit"
                        #organization_list_edit="ngModel" [(ngModel)]="organization_list_id"
                        (ngModelChange)="selected_name='university_id';selected_value=organization_list_id;getCollege(organization_type_id,0,'year')">
                        <mat-option>
                            <ngx-mat-select-search [noEntriesFoundLabel]="'no_records_found_text'|translate"
                                [placeholderLabel]="'search_text'|translate" name="search_university" ngModel
                                (ngModelChange)="searchOrganizations($event)">
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let item of all_organization_list | async" [value]="item['pk_id']" matTooltip='{{ item["organization_name"] }}'>{{
                            item["organization_name"] }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1" *ngIf="is_college">
                <span>{{"admin.assessment.create.college"|translate}}</span>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'admin.assessment.create.select_college' | translate}}"
                        name="college_edit" #college_edit="ngModel" [(ngModel)]="college_id"
                        (ngModelChange)="selected_name='college_id';selected_value=college_id;getYearSemsterGroup(organization_type_id,0,'year')">
                        <mat-option>
                            <ngx-mat-select-search [noEntriesFoundLabel]="'no_records_found_text'|translate"
                                [placeholderLabel]="'search_text'|translate" name="search_college" ngModel
                                (ngModelChange)="searchCollege($event)">
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let item of all_college | async" [value]="''+item['pk_id']" matTooltip='{{
                            item["organization_name"]?item["organization_name"]:item["partner_name"] }}'>{{
                            item["organization_name"]?item["organization_name"]:item["partner_name"] }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1">
                <span>{{"admin.assessment.create.year"|translate}}</span>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'admin.assessment.create.select_year'|translate}}" name="year_edit"
                        #year_edit="ngModel" [(ngModel)]="year_id"
                        (ngModelChange)="selected_name='year_id';selected_value=year_id;getYearSemsterGroup(organization_type_id,year_id,'semester')">
                        <mat-option *ngFor="let item of all_years | async" [value]="item['pk_id']">{{ item["name"] }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1" *ngIf="show_semester_dropdown">
                <span>{{"admin.assessment.create.semester"|translate}}</span>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'admin.assessment.create.select_semester'|translate}}"
                        name="semester_edit" #semester_edit="ngModel" [(ngModel)]="semester_id"
                        (ngModelChange)="selected_name='semester_id';selected_value=semester_id;getYearSemsterGroup(organization_type_id,semester_id,'group')">
                        <mat-option *ngFor="let item of all_semesters | async" [value]="item['pk_id']">{{ item["name"]
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="cm_fltr_sc mr_1" *ngIf="show_group_dropdown">
                <span>{{"admin.assessment.create.group"|translate}}</span>
                <mat-form-field appearance="outline">
                    <mat-select placeholder="{{'admin.assessment.create.select_group'|translate}}" name="group_edit"
                        #group_edit="ngModel" [(ngModel)]="group_id"
                        (ngModelChange)="selected_name='group_id';selected_value=group_id;">
                        <mat-option *ngFor="let item of all_groups | async" [value]="item['pk_id']">{{ item["name"] }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <button type="button" [disabled]="selected_name == '' || selected_value == ''" (click)="getStudents()"
                mat-raised-button color="accent">{{"admin.assessment.create.get_students"|translate}}</button>
            <hr>
            <div class="hgt_20"></div>
            <div class="d-flex">
                <div class="w_49p mr_1p swt_lft">
                    <mat-form-field appearance="outline" class="w_100p">
                        <input matInput [(ngModel)]="search_student" name="search_student_name" autocomplete="off"
                            (keyup)="searchStudents(search_student)" placeholder="{{'admin.assessment.create.srch_student_lft'|translate}}" />
                        <mat-icon (click)="searchStudents(search_student)" matSuffix color="accent">search</mat-icon>
                    </mat-form-field>
                </div>
                <div class="w_50p">
                    <mat-form-field appearance="outline" class="w_100p" color="accent">
                        <input matInput [(ngModel)]="search_selected_student" name="search_selected_student_name" autocomplete="off"
                            (keyup)="searchSelectedStudents(search_selected_student)" placeholder="{{'admin.assessment.create.srch_student_rgt'|translate}}" />
                        <mat-icon (click)="searchSelectedStudents(search_selected_student)" matSuffix color="accent">search</mat-icon> 
                    </mat-form-field>
                </div>
            </div>
            <div class="d-flex zm_swtchusrs">
                <div class="w_49p mr_1p swt_lft">
                    <div class="zm_mvusrs">
                        <div class="zm_mvusrs_hd">{{"admin.assessment.create.student_list"|translate}}</div>
                        <div class="spacer"></div>
                        <div> 
                            <button type="button" matTooltip="{{'admin.assessment.create.add_all_students'|translate}}"
                                [disabled]="students.length<=0" (click)="addStudents()"
                                mat-button color="accent">
                                {{"admin.assessment.create.add_all"|translate}} <mat-icon>chevron_right</mat-icon>
                            </button>
                        </div>
                    </div>
                    <div class="zmsu_cnt">
                        <span class="zmsu_lft" *ngFor="let s of students; let s_index=index;"
                            [hidden]="this.selected_student_ids.includes(s['id'])">
                            <span>{{s['name']}}</span>
                            <button mat-icon-button color="accent" type="button">
                                <mat-icon (click)="addStudent(s)" matTooltip="Add Student">add</mat-icon>
                            </button>
                        </span>
                    </div>
                    <span class="rqrd_txtinfo" *ngIf="students.length<=0">
                        <mat-icon class="mr_05">info</mat-icon> {{"admin.assessment.create.students_list_text"|translate}}
                    </span>
                </div>
                <div class="w_50p swt_rgt">
                    <div class="zm_mvusrs">
                        <div class="zm_mvusrs_hd">{{"admin.assessment.create.selected_students"|translate}}</div>
                        <div class="spacer"></div>
                        <div>
                            <button type="button" matTooltip="{{'admin.assessment.create.remove_all_students'|translate}}"
                                [disabled]="selected_students.length<=0"
                                (click)="selected_students=[];selected_student_ids = [];selected_students_list = [];" mat-button color="warn">
                                <mat-icon>chevron_left</mat-icon> {{"admin.assessment.create.remove_all"|translate}}
                            </button>
                        </div>
                    </div>
                    <div class="zmsu_cnt">
                        <span class="zmsu_lft" *ngFor="let s of selected_students; let s_s_index=index;">
                            <span>{{s['name']}}</span> <button mat-icon-button color="accent" type="button">
                                <mat-icon (click)="removeSelectedStudent(s_s_index, s['id'])" matTooltip="Remove Student"
                                    color="warn">highlight_off</mat-icon>
                            </button>
                        </span>
                    </div>
                    <span class="rqrd_txtinfo" *ngIf="selected_students.length<=0">
                        <mat-icon class="mr_05">info</mat-icon> {{"admin.assessment.create.selected_students_list_text"|translate}}
                    </span>
                </div>
            </div>
            <div class="hgt_10"></div>
            <hr class="line-sep"/>
            <div class="d-flex">
                <div class="spacer"></div>
                <button mat-raised-button color="accent" type="submit">
                    <mat-icon>done</mat-icon> {{"admin.assessment.create.page_title"|translate}}
                </button>
            </div>
        </div>
    </form>
</div>